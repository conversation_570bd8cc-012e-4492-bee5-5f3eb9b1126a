#version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./data/videos:/app/videos
      - ./data/db:/app/db
      - ./data/transcripts:/app/transcripts
    environment:
      - DATABASE_URL=sqlite:///db/tagTok.db
      - VIDEOS_DIR=/app/videos
      - TRANSCRIPTS_DIR=/app/transcripts
      - PYTHONPATH=/app
    depends_on:
      - frontend
      - ollama
    networks:
      - tagTok-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - CHOKIDAR_USEPOLLING=true
    networks:
      - tagTok-network
    restart: unless-stopped

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    networks:
      - tagTok-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "ollama", "list"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  ollama-setup:
    image: ollama/ollama:latest
    depends_on:
      ollama:
        condition: service_healthy
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=http://ollama:11434
    networks:
      - tagTok-network
    command: >
      sh -c "
        echo 'Waiting for Ollama service to be ready...' &&
        sleep 10 &&
        echo 'Pulling llama3.2:3b model...' &&
        ollama pull llama3.2:3b &&
        echo 'Model pulled successfully!' &&
        ollama list
      "
    restart: "no"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./data/videos:/usr/share/nginx/html/videos
    depends_on:
      - backend
      - frontend
    networks:
      - tagTok-network
    restart: unless-stopped

volumes:
  videos:
  db:
  transcripts:
  ollama_data:

networks:
  tagTok-network:
    driver: bridge
