version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./data/videos:/app/videos
      - ./data/db:/app/db
      - ./data/transcripts:/app/transcripts
    environment:
      - DATABASE_URL=sqlite:///db/tagTok.db
      - VIDEOS_DIR=/app/videos
      - TRANSCRIPTS_DIR=/app/transcripts
      - PYTHONPATH=/app
    depends_on:
      - frontend
    networks:
      - tagTok-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - CHOKIDAR_USEPOLLING=true
    networks:
      - tagTok-network
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./data/videos:/usr/share/nginx/html/videos
    depends_on:
      - backend
      - frontend
    networks:
      - tagTok-network
    restart: unless-stopped

volumes:
  videos:
  db:
  transcripts:

networks:
  tagTok-network:
    driver: bridge
