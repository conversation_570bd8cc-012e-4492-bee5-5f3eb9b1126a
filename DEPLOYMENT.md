# tagTok Deployment Guide

This guide covers different deployment scenarios for the tagTok application.

## 🐳 Docker Deployment (Recommended)

### Production Deployment

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-username/tagTok-v2.git
   cd tagTok-v2
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your production settings
   ```

3. **Build and start services**:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d --build
   ```

4. **Verify deployment**:
   ```bash
   curl http://localhost/api/health
   ```

### Development Deployment

```bash
# Start with hot reload
docker-compose up --build

# Or in detached mode
docker-compose up -d --build
```

## 🖥️ Local Development

### Backend Only

```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Only

```bash
cd frontend
npm install
npm start
```

## ☁️ Cloud Deployment

### AWS EC2

1. **Launch EC2 instance** (t3.medium or larger recommended)
2. **Install Docker and Docker Compose**:
   ```bash
   sudo yum update -y
   sudo yum install -y docker
   sudo service docker start
   sudo usermod -a -G docker ec2-user
   
   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   ```

3. **Deploy application**:
   ```bash
   git clone https://github.com/your-username/tagTok-v2.git
   cd tagTok-v2
   docker-compose up -d --build
   ```

4. **Configure security group** to allow HTTP (port 80) access

### Google Cloud Platform

1. **Create Compute Engine instance**
2. **Install Docker**:
   ```bash
   sudo apt-get update
   sudo apt-get install -y docker.io docker-compose
   sudo systemctl start docker
   sudo systemctl enable docker
   sudo usermod -a -G docker $USER
   ```

3. **Deploy application** (same as AWS steps 3-4)

### DigitalOcean Droplet

1. **Create droplet** with Docker pre-installed
2. **Deploy application**:
   ```bash
   git clone https://github.com/your-username/tagTok-v2.git
   cd tagTok-v2
   docker-compose up -d --build
   ```

## 🔧 Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```bash
# Database
DATABASE_URL=sqlite:///db/tagTok.db

# Directories
VIDEOS_DIR=/app/videos
TRANSCRIPTS_DIR=/app/transcripts

# API
REACT_APP_API_URL=http://your-domain.com:8000

# AI Models
WHISPER_MODEL_SIZE=base

# Ports (change if needed)
NGINX_PORT=80
BACKEND_PORT=8000
FRONTEND_PORT=3000
```

### Reverse Proxy (Nginx)

For production, you may want to use a separate Nginx instance:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /api/ {
        proxy_pass http://localhost:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        client_max_body_size 500M;
    }

    location /videos/ {
        proxy_pass http://localhost:8000/videos/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### SSL/HTTPS Setup

Using Let's Encrypt with Certbot:

```bash
# Install Certbot
sudo apt-get install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Monitoring

### Health Checks

```bash
# Application health
curl http://localhost/api/health

# Service status
docker-compose ps

# Resource usage
docker stats
```

### Logs

```bash
# View all logs
docker-compose logs

# Follow specific service
docker-compose logs -f backend

# View with timestamps
docker-compose logs -t backend
```

### Backup

```bash
# Backup database
cp data/db/tagTok.db backups/tagTok_$(date +%Y%m%d_%H%M%S).db

# Backup videos
tar -czf backups/videos_$(date +%Y%m%d_%H%M%S).tar.gz data/videos/

# Automated backup script
#!/bin/bash
BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup database
cp data/db/tagTok.db $BACKUP_DIR/tagTok_$DATE.db

# Backup videos (if needed)
tar -czf $BACKUP_DIR/videos_$DATE.tar.gz data/videos/

# Keep only last 7 days of backups
find $BACKUP_DIR -name "tagTok_*.db" -mtime +7 -delete
find $BACKUP_DIR -name "videos_*.tar.gz" -mtime +7 -delete
```

## 🔒 Security

### Basic Security Measures

1. **Change default ports** if exposed to internet
2. **Use firewall** to restrict access:
   ```bash
   sudo ufw allow 22    # SSH
   sudo ufw allow 80    # HTTP
   sudo ufw allow 443   # HTTPS
   sudo ufw enable
   ```

3. **Regular updates**:
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade

   # Update Docker images
   docker-compose pull
   docker-compose up -d --build
   ```

4. **Secure file permissions**:
   ```bash
   chmod 755 data/
   chmod 644 data/db/tagTok.db
   ```

### Authentication (Optional)

To add basic authentication, modify `nginx.conf`:

```nginx
location / {
    auth_basic "tagTok Access";
    auth_basic_user_file /etc/nginx/.htpasswd;
    proxy_pass http://frontend:3000;
}
```

Create password file:
```bash
sudo htpasswd -c /etc/nginx/.htpasswd username
```

## 🚀 Performance Optimization

### Resource Allocation

```yaml
# In docker-compose.yml
services:
  backend:
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
```

### Database Optimization

```bash
# SQLite optimization
sqlite3 data/db/tagTok.db "VACUUM;"
sqlite3 data/db/tagTok.db "ANALYZE;"
```

### Video Storage

Consider using external storage for large video collections:

```yaml
# Mount external storage
volumes:
  - /mnt/external-storage:/app/videos
```

## 🔄 Updates

### Application Updates

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose down
docker-compose up -d --build

# Or rolling update
docker-compose up -d --build --no-deps backend
docker-compose up -d --build --no-deps frontend
```

### Database Migrations

```bash
# Backup before migration
cp data/db/tagTok.db data/db/tagTok_backup.db

# Run migrations (if any)
docker-compose exec backend python -m alembic upgrade head
```

## 🆘 Troubleshooting

### Common Issues

1. **Port already in use**:
   ```bash
   sudo lsof -i :80
   sudo kill -9 <PID>
   ```

2. **Permission denied**:
   ```bash
   sudo chown -R $USER:$USER data/
   chmod -R 755 data/
   ```

3. **Out of disk space**:
   ```bash
   df -h
   docker system prune -a
   ```

4. **Memory issues**:
   ```bash
   free -h
   # Reduce Whisper model size in backend/utils/ai_utils.py
   ```

### Recovery

```bash
# Reset application (WARNING: Deletes all data)
docker-compose down -v
rm -rf data/
docker-compose up -d --build

# Restore from backup
cp backups/tagTok_YYYYMMDD_HHMMSS.db data/db/tagTok.db
docker-compose restart backend
```
