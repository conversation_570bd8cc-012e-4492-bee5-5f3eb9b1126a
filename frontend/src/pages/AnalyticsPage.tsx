import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  ChartBarIcon, 
  VideoCameraIcon, 
  TagIcon,
  ClockIcon,
  ArrowDownTrayIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

import { analyticsApi, exportApi } from '../utils/api';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import StatsCard from '../components/StatsCard';
import TagChart from '../components/TagChart';
import UploadTimelineChart from '../components/UploadTimelineChart';
import DurationDistributionChart from '../components/DurationDistributionChart';
import LanguageDistributionChart from '../components/LanguageDistributionChart';

const AnalyticsPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState(30);

  // Fetch analytics data
  const {
    data: analytics,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['analytics', timeRange],
    queryFn: () => analyticsApi.getAnalytics(timeRange),
  });

  const handleExportAnalytics = async () => {
    try {
      const blob = await exportApi.exportAnalytics('json', timeRange);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !analytics) {
    return (
      <ErrorMessage 
        message="Failed to load analytics data" 
        onRetry={() => refetch()}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
          <p className="mt-2 text-sm text-gray-700">
            Insights and statistics about your video library
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          {/* Time Range Selector */}
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(parseInt(e.target.value))}
            className="block px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
            <option value={7}>Last 7 days</option>
            <option value={30}>Last 30 days</option>
            <option value={90}>Last 90 days</option>
            <option value={365}>Last year</option>
          </select>
          
          {/* Export Button */}
          <button
            onClick={handleExportAnalytics}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Videos"
          value={analytics.total_videos}
          icon={VideoCameraIcon}
          color="blue"
        />
        <StatsCard
          title="Total Tags"
          value={analytics.total_tags}
          icon={TagIcon}
          color="green"
        />
        <StatsCard
          title="Total Duration"
          value={formatDuration(analytics.total_duration)}
          icon={ClockIcon}
          color="purple"
        />
        <StatsCard
          title="Processed Videos"
          value={`${analytics.processed_videos}/${analytics.total_videos}`}
          subtitle={`${Math.round((analytics.processed_videos / analytics.total_videos) * 100)}% complete`}
          icon={ChartBarIcon}
          color="orange"
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Tags */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Top Tags</h2>
          <TagChart tags={analytics.top_tags} />
        </div>

        {/* Language Distribution */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Language Distribution</h2>
          <LanguageDistributionChart data={analytics.language_distribution} />
        </div>

        {/* Upload Timeline */}
        <div className="bg-white rounded-lg shadow p-6 lg:col-span-2">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Upload Timeline</h2>
          <UploadTimelineChart data={analytics.upload_timeline} />
        </div>

        {/* Duration Distribution */}
        <div className="bg-white rounded-lg shadow p-6 lg:col-span-2">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Video Duration Distribution</h2>
          <DurationDistributionChart data={analytics.duration_distribution} />
        </div>
      </div>

      {/* Processing Status */}
      {analytics.pending_videos > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-center">
            <CalendarIcon className="h-5 w-5 text-yellow-400 mr-2" />
            <h3 className="text-lg font-medium text-yellow-800">Processing Status</h3>
          </div>
          <div className="mt-2 text-sm text-yellow-700">
            <p>
              {analytics.pending_videos} video{analytics.pending_videos !== 1 ? 's' : ''} 
              {' '}pending processing. Transcription and tagging will be completed automatically.
            </p>
          </div>
        </div>
      )}

      {/* Quick Stats */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Statistics</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Average Video Duration</h3>
            <p className="mt-1 text-2xl font-semibold text-gray-900">
              {analytics.total_videos > 0 
                ? formatDuration(analytics.total_duration / analytics.total_videos)
                : '0m'
              }
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Tags per Video</h3>
            <p className="mt-1 text-2xl font-semibold text-gray-900">
              {analytics.total_videos > 0 
                ? (analytics.top_tags.reduce((sum, tag) => sum + tag.usage_count, 0) / analytics.total_videos).toFixed(1)
                : '0'
              }
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Most Popular Tag</h3>
            <p className="mt-1 text-lg font-semibold text-gray-900">
              {analytics.top_tags.length > 0 
                ? `${analytics.top_tags[0].name} (${analytics.top_tags[0].usage_count})`
                : 'None'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsPage;
