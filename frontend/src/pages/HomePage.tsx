import React, { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline';

import { videoApi, tagApi } from '../utils/api';
import { VideoFilter, PaginationParams, Video, Tag, TagCloudData } from '../types';
import VideoGrid from '../components/VideoGrid';
import TagCloud from '../components/TagCloud';
import SearchBar from '../components/SearchBar';
import FilterPanel from '../components/FilterPanel';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const HomePage: React.FC = () => {
  const [filters, setFilters] = useState<VideoFilter>({});
  const [pagination, setPagination] = useState<PaginationParams>({ skip: 0, limit: 24 });
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch videos
  const {
    data: videos = [],
    isPending: videosLoading,
    error: videosError,
    refetch: refetchVideos
  } = useQuery<Video[]>({
    queryKey: ['videos', pagination, filters],
    queryFn: () => videoApi.getVideos(pagination, filters),
    placeholderData: (previousData) => previousData,
  });

  // Fetch tags for filter panel
  const {
    data: tags = [],
    isPending: tagsLoading,
  } = useQuery<Tag[]>({
    queryKey: ['tags'],
    queryFn: () => tagApi.getTags({ skip: 0, limit: 100 }),
  });

  // Fetch tag cloud data
  const {
    data: tagCloudData = [],
  } = useQuery<TagCloudData[]>({
    queryKey: ['tagCloud'],
    queryFn: () => tagApi.getTagCloudData(),
  });

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setFilters(prev => ({ ...prev, search: query || undefined }));
    setPagination(prev => ({ ...prev, skip: 0 })); // Reset to first page
  };

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<VideoFilter>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, skip: 0 })); // Reset to first page
  };

  // Handle tag click from tag cloud
  const handleTagClick = (tagName: string) => {
    const currentTags = filters.tags || [];
    const newTags = currentTags.includes(tagName)
      ? currentTags.filter(t => t !== tagName)
      : [...currentTags, tagName];
    
    handleFilterChange({ tags: newTags.length > 0 ? newTags : undefined });
  };

  // Handle pagination
  const handleLoadMore = () => {
    setPagination(prev => ({ ...prev, skip: prev.skip + prev.limit }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
    setPagination({ skip: 0, limit: 24 });
  };

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return !!(filters.search || filters.tags?.length || filters.language || filters.processed !== undefined);
  }, [filters]);

  if (videosError) {
    return (
      <ErrorMessage 
        message="Failed to load videos" 
        onRetry={() => refetchVideos()}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Video Library</h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage and organize your TikTok videos with AI-powered tagging
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
            {hasActiveFilters && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                Active
              </span>
            )}
          </button>
        </div>
      </div>

      {/* Search Bar */}
      <SearchBar
        value={searchQuery}
        onChange={handleSearch}
        placeholder="Search videos by title, transcript, or filename..."
      />

      {/* Filter Panel */}
      {showFilters && (
        <FilterPanel
          filters={filters}
          tags={tags}
          onFilterChange={handleFilterChange}
          onClearFilters={clearFilters}
          hasActiveFilters={hasActiveFilters}
        />
      )}

      {/* Tag Cloud */}
      {tagCloudData.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Popular Tags</h2>
          <TagCloud
            tags={tagCloudData}
            selectedTags={filters.tags || []}
            onTagClick={handleTagClick}
          />
        </div>
      )}

      {/* Video Grid */}
      <div className="bg-white rounded-lg shadow">
        {videosLoading && pagination.skip === 0 ? (
          <div className="flex justify-center items-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <VideoGrid
            videos={videos}
            onLoadMore={handleLoadMore}
            hasMore={videos.length === pagination.limit}
            loading={videosLoading}
          />
        )}
      </div>

      {/* Empty State */}
      {!videosLoading && videos.length === 0 && (
        <div className="text-center py-12">
          <div className="mx-auto h-12 w-12 text-gray-400">
            <MagnifyingGlassIcon />
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No videos found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {hasActiveFilters 
              ? 'Try adjusting your search criteria or filters.'
              : 'Get started by uploading your first video.'
            }
          </p>
          {hasActiveFilters && (
            <div className="mt-6">
              <button
                onClick={clearFilters}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Clear filters
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default HomePage;
