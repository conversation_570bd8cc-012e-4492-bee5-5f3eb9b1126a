export interface Video {
  id: number;
  filename: string;
  original_filename: string;
  title: string;
  file_path: string;
  file_size: number;
  duration?: number;
  width?: number;
  height?: number;
  fps?: number;
  thumbnail_path?: string;
  transcript?: string;
  transcript_language?: string;
  upload_date: string;
  processed: boolean;
  processing_status: string;
  tags: Tag[];
}

export interface Tag {
  id: number;
  name: string;
  color: string;
  description?: string;
  created_date: string;
  usage_count: number;
}

export interface VideoFilter {
  tags?: string[];
  search?: string;
  language?: string;
  processed?: boolean;
  date_from?: string;
  date_to?: string;
}

export interface AnalyticsData {
  total_videos: number;
  total_tags: number;
  total_duration: number;
  processed_videos: number;
  pending_videos: number;
  top_tags: Array<{
    id: number;
    name: string;
    color: string;
    usage_count: number;
    description?: string;
  }>;
  language_distribution: Record<string, number>;
  upload_timeline: Array<{
    date: string;
    count: number;
  }>;
  duration_distribution: Record<string, number>;
}

export interface UploadResponse {
  message: string;
  uploaded_files: string[];
  failed_files: Array<{
    filename: string;
    error: string;
  }>;
  total_uploaded: number;
}

export interface ProcessingJob {
  id: number;
  video_id: number;
  job_type: string;
  status: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
}

export interface TagCloudData {
  id: number;
  name: string;
  color: string;
  description?: string;
  usage_count: number;
  size: number;
}

export interface ExportOptions {
  format: 'csv' | 'json';
  include_transcript: boolean;
  include_tags: boolean;
  tag_filter?: string[];
}

export interface ApiError {
  detail: string;
  error_code?: string;
}

export interface PaginationParams {
  skip: number;
  limit: number;
}

export interface VideoUpdateData {
  title?: string;
}

export interface TagCreateData {
  name: string;
  color: string;
  description?: string;
}

export interface TagUpdateData {
  name?: string;
  color?: string;
  description?: string;
}
