import React, { useRef, useEffect } from 'react';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

import { Video } from '../types';
import { videoApi } from '../utils/api';

interface VideoPlayerProps {
  video: Video;
  className?: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ video, className = '' }) => {
  const videoRef = useRef<HTMLDivElement>(null);
  const playerRef = useRef<any>(null);

  useEffect(() => {
    // Make sure Video.js player is only initialized once
    if (!playerRef.current) {
      const videoElement = document.createElement('video-js');
      
      videoElement.classList.add('vjs-big-play-centered');
      videoRef.current?.appendChild(videoElement);

      const player = playerRef.current = videojs(videoElement, {
        autoplay: false,
        controls: true,
        responsive: true,
        fluid: false,
        fill: false,
        preload: 'metadata',
        playbackRates: [0.5, 1, 1.25, 1.5, 2],
        aspectRatio: video.width && video.height ? `${video.width}:${video.height}` : '16:9',
        sources: [{
          src: videoApi.getVideoUrl(video.filename),
          type: 'video/mp4'
        }],
        poster: video.thumbnail_path
          ? videoApi.getThumbnailUrl(video.thumbnail_path.split('/').pop() || '')
          : undefined,
      }, () => {
        console.log('Video.js player initialized');
      });

      // Handle player errors
      player.on('error', () => {
        const error = player.error();
        console.error('Video player error:', error);
      });
    }
  }, [video]);

  // Dispose the Video.js player when the functional component unmounts
  useEffect(() => {
    const player = playerRef.current;

    return () => {
      if (player && !player.isDisposed()) {
        player.dispose();
        playerRef.current = null;
      }
    };
  }, []);

  return (
    <div className={`bg-white rounded-lg shadow overflow-hidden ${className}`}>
      <div className="bg-black flex items-center justify-center" style={{ minHeight: '300px' }}>
        <div ref={videoRef} className="max-w-full max-h-full" />
      </div>

      {/* Video metadata below player */}
      <div className="p-4 border-t border-gray-200">
        <h3 className="font-medium text-gray-900">{video.title}</h3>
        <p className="text-sm text-gray-500 mt-1">
          {video.original_filename}
        </p>
        {video.width && video.height && (
          <p className="text-xs text-gray-400 mt-1">
            {video.width} × {video.height}
          </p>
        )}
      </div>
    </div>
  );
};

export default VideoPlayer;
