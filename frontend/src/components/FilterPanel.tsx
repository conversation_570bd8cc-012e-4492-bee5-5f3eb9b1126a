import React from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { VideoFilter, Tag } from '../types';

interface FilterPanelProps {
  filters: VideoFilter;
  tags: Tag[];
  onFilterChange: (filters: Partial<VideoFilter>) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  tags,
  onFilterChange,
  onClearFilters,
  hasActiveFilters,
}) => {
  const handleTagToggle = (tagName: string) => {
    const currentTags = filters.tags || [];
    const newTags = currentTags.includes(tagName)
      ? currentTags.filter(t => t !== tagName)
      : [...currentTags, tagName];
    
    onFilterChange({ tags: newTags.length > 0 ? newTags : undefined });
  };

  const handleProcessedChange = (value: string) => {
    if (value === 'all') {
      onFilterChange({ processed: undefined });
    } else {
      onFilterChange({ processed: value === 'true' });
    }
  };

  const getUniqueLanguages = () => {
    const languages = new Set<string>();
    // This would ideally come from the API, but for now we'll use common ones
    return ['en', 'es', 'fr', 'de', 'it', 'pt', 'zh', 'ja', 'ko'];
  };

  return (
    <div className="bg-white rounded-lg shadow p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Filters</h3>
        {hasActiveFilters && (
          <button
            onClick={onClearFilters}
            className="text-sm text-primary-600 hover:text-primary-700 font-medium"
          >
            Clear all
          </button>
        )}
      </div>

      {/* Processing Status Filter */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Processing Status
        </label>
        <select
          value={filters.processed === undefined ? 'all' : filters.processed.toString()}
          onChange={(e) => handleProcessedChange(e.target.value)}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="all">All videos</option>
          <option value="true">Processed</option>
          <option value="false">Pending</option>
        </select>
      </div>

      {/* Language Filter */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Language
        </label>
        <select
          value={filters.language || ''}
          onChange={(e) => onFilterChange({ language: e.target.value || undefined })}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="">All languages</option>
          {getUniqueLanguages().map((lang) => (
            <option key={lang} value={lang}>
              {lang.toUpperCase()}
            </option>
          ))}
        </select>
      </div>

      {/* Tags Filter */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Tags ({filters.tags?.length || 0} selected)
        </label>
        <div className="max-h-48 overflow-y-auto border border-gray-200 rounded-md p-3">
          {tags.length === 0 ? (
            <p className="text-sm text-gray-500">No tags available</p>
          ) : (
            <div className="space-y-2">
              {tags.map((tag) => {
                const isSelected = filters.tags?.includes(tag.name) || false;
                return (
                  <label
                    key={tag.id}
                    className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-1 rounded"
                  >
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => handleTagToggle(tag.name)}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                    <div className="flex items-center space-x-2 flex-1">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: tag.color }}
                      />
                      <span className="text-sm text-gray-900">{tag.name}</span>
                      <span className="text-xs text-gray-500">({tag.usage_count})</span>
                    </div>
                  </label>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Active Filters
          </label>
          <div className="flex flex-wrap gap-2">
            {filters.tags?.map((tagName) => (
              <span
                key={tagName}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
              >
                {tagName}
                <button
                  onClick={() => handleTagToggle(tagName)}
                  className="ml-1 text-primary-600 hover:text-primary-800"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            ))}
            {filters.processed !== undefined && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {filters.processed ? 'Processed' : 'Pending'}
                <button
                  onClick={() => onFilterChange({ processed: undefined })}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            {filters.language && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                {filters.language.toUpperCase()}
                <button
                  onClick={() => onFilterChange({ language: undefined })}
                  className="ml-1 text-green-600 hover:text-green-800"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterPanel;
