import axios from 'axios';
import {
  Video,
  Tag,
  AnalyticsData,
  UploadResponse,
  VideoFilter,
  PaginationParams,
  VideoUpdateData,
  TagCreateData,
  TagUpdateData,
  ExportOptions,
  CategoryResponse
} from '../types';

// Enhanced mobile-friendly API URL detection
const getApiBaseUrl = () => {
  // If explicitly set, use that
  if (process.env.REACT_APP_API_URL) {
    console.log('Using explicit API URL:', process.env.REACT_APP_API_URL);
    return process.env.REACT_APP_API_URL;
  }

  // Get current location details
  const hostname = window.location.hostname;
  const protocol = window.location.protocol;
  const port = window.location.port;

  console.log('Current location:', { hostname, protocol, port });

  // If accessing via IP address (mobile/network), use the same IP for API
  if (hostname !== 'localhost' && hostname !== '127.0.0.1') {
    const apiUrl = `${protocol}//${hostname}:8000`;
    console.log('Mobile/Network access detected, using API URL:', apiUrl);
    return apiUrl;
  }

  // Default to localhost for desktop development
  const defaultUrl = 'http://localhost:8000';
  console.log('Desktop access detected, using API URL:', defaultUrl);
  return defaultUrl;
};

const API_BASE_URL = getApiBaseUrl();
console.log('Final API Base URL:', API_BASE_URL);

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 second timeout for mobile connections
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use((config) => {
  console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
  console.log(`API Base URL: ${API_BASE_URL}`);
  console.log(`Current hostname: ${window.location.hostname}`);
  return config;
});

// Enhanced response interceptor for mobile debugging
api.interceptors.response.use(
  (response) => {
    console.log('API Success:', {
      url: response.config?.url,
      status: response.status,
      dataLength: response.data?.length || 'N/A'
    });
    return response;
  },
  (error) => {
    const errorDetails = {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      baseURL: error.config?.baseURL,
      fullUrl: error.config?.baseURL + error.config?.url,
      networkError: error.code === 'NETWORK_ERROR' || error.message.includes('Network Error'),
      timeoutError: error.code === 'ECONNABORTED' || error.message.includes('timeout'),
      corsError: error.message.includes('CORS') || error.message.includes('Access-Control'),
      hostname: window.location.hostname,
      userAgent: navigator.userAgent
    };

    console.error('API Error Details:', errorDetails);

    // Add user-friendly error messages for mobile debugging
    if (errorDetails.networkError) {
      console.error('Network Error: Cannot reach the server. Check if backend is running on port 8000.');
    }
    if (errorDetails.corsError) {
      console.error('CORS Error: Cross-origin request blocked. Check CORS configuration.');
    }
    if (errorDetails.timeoutError) {
      console.error('Timeout Error: Request took too long. Check network connection.');
    }

    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Video API
// Mobile connection test function
export const testMobileConnection = async () => {
  try {
    console.log('Testing mobile connection to:', API_BASE_URL);
    const response = await api.get('/health');
    console.log('Mobile connection test successful:', response.data);
    return { success: true, data: response.data };
  } catch (error: any) {
    console.error('Mobile connection test failed:', error);
    return {
      success: false,
      error: error.message,
      details: {
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: error.config?.url,
        baseURL: error.config?.baseURL
      }
    };
  }
};

export const videoApi = {
  // Get videos with filtering and pagination
  getVideos: async (
    pagination: PaginationParams = { skip: 0, limit: 50 },
    filters?: VideoFilter
  ): Promise<Video[]> => {
    const params = new URLSearchParams();
    params.append('skip', pagination.skip.toString());
    params.append('limit', pagination.limit.toString());
    
    if (filters?.tags?.length) {
      params.append('tags', filters.tags.join(','));
    }
    if (filters?.search) {
      params.append('search', filters.search);
    }
    if (filters?.language) {
      params.append('language', filters.language);
    }
    if (filters?.processed !== undefined) {
      params.append('processed', filters.processed.toString());
    }
    if (filters?.category) {
      params.append('category', filters.category);
    }
    
    const response = await api.get(`/videos?${params.toString()}`);
    return response.data;
  },

  // Get single video
  getVideo: async (id: number): Promise<Video> => {
    const response = await api.get(`/videos/${id}`);
    return response.data;
  },

  // Update video
  updateVideo: async (id: number, data: VideoUpdateData): Promise<Video> => {
    const response = await api.put(`/videos/${id}`, data);
    return response.data;
  },

  // Delete video
  deleteVideo: async (id: number): Promise<void> => {
    await api.delete(`/videos/${id}`);
  },

  // Upload videos
  uploadVideos: async (
    files: FileList,
    onProgress?: (progress: number) => void
  ): Promise<UploadResponse> => {
    const formData = new FormData();
    Array.from(files).forEach(file => {
      formData.append('files', file);
    });

    const response = await api.post('/videos/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 300000, // 5 minutes for large uploads
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
    return response.data;
  },

  // Get video transcript
  getTranscript: async (id: number): Promise<{ video_id: number; transcript: string; language: string }> => {
    const response = await api.get(`/videos/${id}/transcript`);
    return response.data;
  },

  // Reprocess video
  reprocessVideo: async (id: number): Promise<void> => {
    await api.post(`/videos/${id}/reprocess`);
  },

  // Download video
  downloadVideo: async (id: number): Promise<Blob> => {
    const response = await api.get(`/videos/${id}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Get video file URL
  getVideoUrl: (filename: string): string => {
    return `${API_BASE_URL}/videos/file/${filename}`;
  },

  // Get thumbnail URL
  getThumbnailUrl: (filename: string): string => {
    return `${API_BASE_URL}/videos/thumbnail/${filename}`;
  },

  // Get available categories
  getCategories: async (): Promise<string[]> => {
    const response = await api.get('/videos/categories');
    return response.data.categories;
  },
};

// Tag API
export const tagApi = {
  // Get tags
  getTags: async (
    pagination: PaginationParams = { skip: 0, limit: 100 },
    search?: string,
    sortBy: string = 'usage_count',
    order: string = 'desc'
  ): Promise<Tag[]> => {
    const params = new URLSearchParams();
    params.append('skip', pagination.skip.toString());
    params.append('limit', pagination.limit.toString());
    params.append('sort_by', sortBy);
    params.append('order', order);
    
    if (search) {
      params.append('search', search);
    }
    
    const response = await api.get(`/tags?${params.toString()}`);
    return response.data;
  },

  // Get single tag
  getTag: async (id: number): Promise<Tag> => {
    const response = await api.get(`/tags/${id}`);
    return response.data;
  },

  // Create tag
  createTag: async (data: TagCreateData): Promise<Tag> => {
    const response = await api.post('/tags', data);
    return response.data;
  },

  // Update tag
  updateTag: async (id: number, data: TagUpdateData): Promise<Tag> => {
    const response = await api.put(`/tags/${id}`, data);
    return response.data;
  },

  // Delete tag
  deleteTag: async (id: number): Promise<void> => {
    await api.delete(`/tags/${id}`);
  },

  // Add tag to video
  addTagToVideo: async (tagId: number, videoId: number): Promise<void> => {
    await api.post(`/tags/${tagId}/videos/${videoId}`);
  },

  // Remove tag from video
  removeTagFromVideo: async (tagId: number, videoId: number): Promise<void> => {
    await api.delete(`/tags/${tagId}/videos/${videoId}`);
  },

  // Get tag cloud data
  getTagCloudData: async () => {
    const response = await api.get('/tags/cloud/data');
    return response.data;
  },
};

// Analytics API
export const analyticsApi = {
  // Get analytics
  getAnalytics: async (days: number = 30): Promise<AnalyticsData> => {
    const response = await api.get(`/analytics?days=${days}`);
    return response.data;
  },

  // Get summary
  getSummary: async () => {
    const response = await api.get('/analytics/summary');
    return response.data;
  },

  // Get top tags
  getTopTags: async (limit: number = 10) => {
    const response = await api.get(`/analytics/tags/top?limit=${limit}`);
    return response.data;
  },

  // Get upload timeline
  getUploadTimeline: async (days: number = 30) => {
    const response = await api.get(`/analytics/videos/timeline?days=${days}`);
    return response.data;
  },

  // Get duration distribution
  getDurationDistribution: async () => {
    const response = await api.get('/analytics/videos/duration-distribution');
    return response.data;
  },

  // Get language distribution
  getLanguageDistribution: async () => {
    const response = await api.get('/analytics/languages');
    return response.data;
  },

  // Get processing status
  getProcessingStatus: async () => {
    const response = await api.get('/analytics/processing-status');
    return response.data;
  },
};

// Export API
export const exportApi = {
  // Export videos
  exportVideos: async (options: ExportOptions): Promise<Blob> => {
    const params = new URLSearchParams();
    params.append('format', options.format);
    params.append('include_transcript', options.include_transcript.toString());
    params.append('include_tags', options.include_tags.toString());
    
    if (options.tag_filter?.length) {
      params.append('tag_filter', options.tag_filter.join(','));
    }
    
    const response = await api.get(`/export/videos?${params.toString()}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Export tags
  exportTags: async (format: 'csv' | 'json' = 'csv'): Promise<Blob> => {
    const response = await api.get(`/export/tags?format=${format}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Export analytics
  exportAnalytics: async (format: 'json' = 'json', days: number = 30): Promise<Blob> => {
    const response = await api.get(`/export/analytics?format=${format}&days=${days}`, {
      responseType: 'blob',
    });
    return response.data;
  },
};

// Health check
export const healthApi = {
  check: async () => {
    const response = await api.get('/health');
    return response.data;
  },
};

export default api;
