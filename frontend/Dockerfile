FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy public directory first (ensure it's included)
COPY public/ ./public/

# Copy source code
COPY src/ ./src/

# Copy other necessary files
COPY tsconfig.json ./
COPY tailwind.config.js ./
COPY postcss.config.js ./

# Verify public directory exists
RUN ls -la public/ && echo "Contents of public directory:" && ls -la public/

# Expose port
EXPOSE 3000

# Start development server
CMD ["npm", "start"]
