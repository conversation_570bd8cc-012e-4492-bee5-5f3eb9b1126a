FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy all source code (this includes public, src, and config files)
COPY . .

# Verify public directory exists and show contents
RUN echo "Checking if public directory exists..." && \
    ls -la . && \
    echo "Contents of public directory:" && \
    ls -la public/ || echo "Public directory not found!"

# Expose port
EXPOSE 3000

# Start development server
CMD ["npm", "start"]
