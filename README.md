# tagTok - TikTok Video Organization Platform

A comprehensive fullstack web application for uploading, transcribing, and organizing TikTok videos with AI-powered tagging and analytics.

## 🌟 Features

### Core Functionality
- 📹 **Multi-Video Upload**: Drag & drop interface for uploading multiple TikTok videos
- 🎯 **AI-Powered Transcription**: Automatic transcription using OpenAI Whisper (CPU-only)
- 🏷️ **Smart Tagging**: AI-generated tags using spaCy and KeyBERT for content analysis
- 🔍 **Advanced Search**: Search by tags, transcript content, filename, or metadata
- 📊 **Analytics Dashboard**: Comprehensive insights into your video library
- 📱 **Mobile-Responsive**: Optimized for desktop, tablet, and mobile devices

### Video Management
- 🎬 **Video Player**: Built-in video player with controls and thumbnails
- ✏️ **Metadata Editing**: Edit video titles, descriptions, and custom tags
- 🗂️ **Tag Organization**: Create, edit, and manage colored tags with descriptions
- 📥 **Download**: Download original video files
- 🗑️ **Smart Deletion**: Delete videos and automatically clean up unused tags

### Data & Analytics
- 📈 **Usage Statistics**: Track upload trends, tag popularity, and processing status
- 🌍 **Language Analytics**: Automatic language detection and distribution charts
- ⏱️ **Duration Analysis**: Video length distribution and statistics
- 📤 **Export Options**: Export metadata as CSV or JSON files
- 💾 **Local Storage**: All data stored locally with no cloud dependencies

## 🛠️ Tech Stack

### Backend
- **Framework**: Python FastAPI with async support
- **Database**: SQLite with SQLAlchemy ORM
- **AI/ML**: OpenAI Whisper, spaCy, KeyBERT, sentence-transformers
- **Video Processing**: OpenCV, MoviePy, FFmpeg
- **API**: RESTful API with automatic OpenAPI documentation

### Frontend
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with responsive design
- **State Management**: TanStack Query (React Query) for server state
- **UI Components**: Headless UI, Heroicons
- **Video Player**: Video.js with custom styling
- **File Upload**: React Dropzone with drag & drop

### Infrastructure
- **Containerization**: Docker & Docker Compose
- **Reverse Proxy**: Nginx for routing and static file serving
- **Development**: Hot reload for both frontend and backend
- **Production**: Optimized builds with health checks

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- At least 4GB RAM (recommended for AI processing)
- 10GB+ free disk space for videos and models

### Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-username/tagTok-v2.git
   cd tagTok-v2
   ```

2. **Start the application**:
   ```bash
   docker-compose up --build
   ```

3. **Access the application**:
   - **Main App**: http://localhost (or http://localhost:80)
   - **Frontend**: http://localhost:3000 (direct access)
   - **Backend API**: http://localhost:8000
   - **API Documentation**: http://localhost:8000/docs

### First Steps
1. Navigate to the Upload page
2. Drag and drop your TikTok videos
3. Wait for processing (transcription + tagging)
4. Explore your videos in the gallery
5. Check out the analytics dashboard

## 📁 Project Structure

```
tagTok-v2/
├── backend/                    # FastAPI backend application
│   ├── main.py                # Application entry point
│   ├── models/                # Database models and schemas
│   │   ├── database.py        # SQLAlchemy models
│   │   └── schemas.py         # Pydantic schemas
│   ├── routers/               # API route handlers
│   │   ├── videos.py          # Video management endpoints
│   │   ├── tags.py            # Tag management endpoints
│   │   ├── analytics.py       # Analytics endpoints
│   │   └── export.py          # Data export endpoints
│   ├── services/              # Business logic layer
│   │   ├── video_service.py   # Video operations
│   │   ├── tag_service.py     # Tag operations
│   │   ├── analytics_service.py # Analytics calculations
│   │   ├── export_service.py  # Data export logic
│   │   └── processing_service.py # AI processing pipeline
│   ├── utils/                 # Utility functions
│   │   ├── video_utils.py     # Video processing utilities
│   │   └── ai_utils.py        # AI/ML utilities
│   ├── requirements.txt       # Python dependencies
│   └── Dockerfile            # Backend container config
├── frontend/                  # React frontend application
│   ├── src/
│   │   ├── components/        # Reusable React components
│   │   │   ├── Layout.tsx     # Main layout component
│   │   │   ├── VideoGrid.tsx  # Video gallery grid
│   │   │   ├── VideoCard.tsx  # Individual video cards
│   │   │   ├── VideoPlayer.tsx # Video player component
│   │   │   ├── TagManager.tsx # Tag management interface
│   │   │   ├── SearchBar.tsx  # Search functionality
│   │   │   ├── FilterPanel.tsx # Advanced filtering
│   │   │   └── ...           # Other components
│   │   ├── pages/            # Page components
│   │   │   ├── HomePage.tsx   # Video gallery page
│   │   │   ├── VideoDetailPage.tsx # Video detail view
│   │   │   ├── UploadPage.tsx # Upload interface
│   │   │   └── AnalyticsPage.tsx # Analytics dashboard
│   │   ├── utils/            # Utility functions
│   │   │   └── api.ts        # API client functions
│   │   ├── types/            # TypeScript type definitions
│   │   └── hooks/            # Custom React hooks
│   ├── package.json          # Node.js dependencies
│   └── Dockerfile           # Frontend container config
├── data/                     # Persistent data (created automatically)
│   ├── videos/              # Uploaded video files
│   ├── db/                  # SQLite database
│   └── transcripts/         # Transcript files
├── docker-compose.yml       # Multi-container orchestration
├── nginx.conf              # Nginx reverse proxy config
└── README.md              # This file
```

## 🔧 Development

### Local Development Setup

#### Backend Development
```bash
# Navigate to backend directory
cd backend

# Create virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### Frontend Development
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm start
```

#### Full Stack Development
```bash
# Run both backend and frontend with hot reload
docker-compose up --build

# Or run in detached mode
docker-compose up -d --build
```

### Development Tools

#### API Documentation
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

#### Database Management
```bash
# Access SQLite database directly
sqlite3 data/db/tagTok.db

# View database schema
.schema

# List all tables
.tables
```

#### Debugging
```bash
# View real-time logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Access container shell
docker-compose exec backend bash
docker-compose exec frontend bash
```

## 📡 API Reference

### Video Endpoints
- `GET /videos` - List videos with filtering and pagination
  - Query params: `skip`, `limit`, `tags`, `search`, `language`, `processed`
- `POST /videos/upload` - Upload multiple video files
- `GET /videos/{id}` - Get video details by ID
- `PUT /videos/{id}` - Update video metadata
- `DELETE /videos/{id}` - Delete video and associated files
- `GET /videos/{id}/transcript` - Get video transcript
- `POST /videos/{id}/reprocess` - Reprocess video (transcription + tagging)
- `GET /videos/{id}/download` - Download original video file

### Tag Endpoints
- `GET /tags` - List all tags with sorting and filtering
- `POST /tags` - Create new tag
- `GET /tags/{id}` - Get tag details
- `PUT /tags/{id}` - Update tag
- `DELETE /tags/{id}` - Delete tag
- `POST /tags/{tag_id}/videos/{video_id}` - Add tag to video
- `DELETE /tags/{tag_id}/videos/{video_id}` - Remove tag from video
- `GET /tags/cloud/data` - Get tag cloud visualization data

### Analytics Endpoints
- `GET /analytics` - Get comprehensive analytics
- `GET /analytics/summary` - Get basic statistics summary
- `GET /analytics/tags/top` - Get top tags by usage
- `GET /analytics/videos/timeline` - Get upload timeline
- `GET /analytics/videos/duration-distribution` - Get duration distribution
- `GET /analytics/languages` - Get language distribution
- `GET /analytics/processing-status` - Get processing status breakdown

### Export Endpoints
- `GET /export/videos` - Export video metadata (CSV/JSON)
- `GET /export/tags` - Export tag data (CSV/JSON)
- `GET /export/analytics` - Export analytics data (JSON)

### Utility Endpoints
- `GET /health` - Health check endpoint
- `GET /videos/file/{filename}` - Serve video files
- `GET /videos/thumbnail/{filename}` - Serve thumbnail images

## ⚙️ Configuration

### Environment Variables

#### Backend Configuration
```yaml
# In docker-compose.yml
environment:
  - DATABASE_URL=sqlite:///db/tagTok.db
  - VIDEOS_DIR=/app/videos
  - TRANSCRIPTS_DIR=/app/transcripts
  - PYTHONPATH=/app
```

#### Frontend Configuration
```yaml
# In docker-compose.yml
environment:
  - REACT_APP_API_URL=http://localhost:8000
  - CHOKIDAR_USEPOLLING=true
```

### Docker Compose Configuration

#### Port Mapping
```yaml
services:
  nginx:
    ports:
      - "80:80"        # Main application
  backend:
    ports:
      - "8000:8000"    # Backend API
  frontend:
    ports:
      - "3000:3000"    # Frontend dev server
```

#### Volume Mapping
```yaml
volumes:
  - ./data/videos:/app/videos      # Video storage
  - ./data/db:/app/db              # Database storage
  - ./data/transcripts:/app/transcripts  # Transcript storage
```

### AI Model Configuration

#### Whisper Model Sizes
- `tiny`: Fastest, least accurate (~39 MB)
- `base`: Good balance (~74 MB) - **Default**
- `small`: Better accuracy (~244 MB)
- `medium`: High accuracy (~769 MB)
- `large`: Best accuracy (~1550 MB)

Change model size in `backend/utils/ai_utils.py`:
```python
self.model_size = "base"  # Change to desired size
```

## 🐛 Troubleshooting

### Common Issues

#### Port Conflicts
```bash
# Check what's using ports
lsof -i :80
lsof -i :3000
lsof -i :8000

# Change ports in docker-compose.yml if needed
```

#### Storage Permissions
```bash
# Ensure Docker has access to data directory
chmod -R 755 ./data
chown -R $USER:$USER ./data
```

#### Memory Issues
- Whisper requires 4GB+ RAM for larger models
- Reduce model size if experiencing memory issues
- Monitor memory usage: `docker stats`

#### Video Processing Issues
```bash
# Check FFmpeg installation in container
docker-compose exec backend ffmpeg -version

# Verify video file formats
docker-compose exec backend file /app/videos/filename.mp4
```

#### Database Issues
```bash
# Reset database (WARNING: Deletes all data)
rm -rf data/db/*
docker-compose restart backend

# Backup database
cp data/db/tagTok.db data/db/tagTok_backup.db
```

### Performance Optimization

#### Backend Optimization
- Use smaller Whisper models for faster processing
- Implement video compression for large files
- Add database indexing for large datasets

#### Frontend Optimization
- Enable lazy loading for video thumbnails
- Implement virtual scrolling for large video lists
- Use React.memo for expensive components

### Monitoring and Logs

#### Application Logs
```bash
# View all logs
docker-compose logs

# Follow specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f nginx

# View logs with timestamps
docker-compose logs -t backend
```

#### Health Checks
```bash
# Check application health
curl http://localhost/api/health

# Check individual services
curl http://localhost:8000/health
curl http://localhost:3000
```

## 🧪 Testing

### Backend Testing
```bash
cd backend
pytest tests/
```

### Frontend Testing
```bash
cd frontend
npm test
```

### End-to-End Testing
```bash
# Run full application
docker-compose up -d

# Test upload functionality
curl -X POST -F "files=@test_video.mp4" http://localhost:8000/videos/upload

# Test API endpoints
curl http://localhost:8000/videos
curl http://localhost:8000/tags
curl http://localhost:8000/analytics
```

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For support, please open an issue on GitHub or contact the development team.

---

**tagTok** - Organize your TikTok videos with the power of AI! 🚀
