# tagTok - TikTok Video Organization Platform

A fullstack web application for uploading, transcribing, and organizing TikTok videos with AI-powered tagging.

## Features

- 📹 **Video Upload & Management**: Upload multiple TikTok videos with drag & drop
- 🎯 **AI-Powered Tagging**: Automatic tag suggestions using Whisper transcription + NLP
- 🔍 **Smart Search**: Search videos by tags, transcript content, or metadata
- 📊 **Analytics Dashboard**: Video stats, tag usage, upload trends
- 📱 **Mobile-Friendly**: Responsive design for all devices
- 💾 **Local Storage**: All data stored locally (no cloud dependencies)
- 🎨 **Tag Management**: Create, edit, and organize custom tags with colors
- 📄 **Export**: CSV/JSON export of all video metadata

## Tech Stack

- **Backend**: Python FastAPI + SQLite
- **Frontend**: React + TypeScript + Tailwind CSS
- **AI/ML**: OpenAI Whisper (CPU), spaCy, KeyBERT
- **Deployment**: Docker Compose
- **Video Processing**: FFmpeg, OpenCV

## Quick Start

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd tagTok-v2
   ```

2. **Start the application**:
   ```bash
   docker-compose up --build
   ```

3. **Access the application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Project Structure

```
tagTok-v2/
├── backend/                 # FastAPI backend
│   ├── app/                # Application code
│   ├── models/             # Database models
│   ├── routers/            # API endpoints
│   ├── services/           # Business logic
│   └── Dockerfile
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   └── utils/          # Utilities
│   └── Dockerfile
├── data/                   # Persistent data
│   ├── videos/             # Uploaded videos
│   ├── db/                 # SQLite database
│   └── transcripts/        # Transcript files
├── docker-compose.yml      # Container orchestration
└── nginx.conf             # Reverse proxy config
```

## Development

### Backend Development

```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Development

```bash
cd frontend
npm install
npm start
```

## API Endpoints

- `GET /videos` - List all videos with filtering
- `POST /videos/upload` - Upload new videos
- `GET /videos/{id}` - Get video details
- `DELETE /videos/{id}` - Delete video
- `GET /videos/{id}/transcript` - Get video transcript
- `GET /tags` - List all tags
- `POST /tags` - Create new tag
- `PUT /tags/{id}` - Update tag
- `DELETE /tags/{id}` - Delete tag
- `GET /analytics` - Get analytics data
- `GET /export` - Export metadata as CSV/JSON

## Configuration

Environment variables can be set in `docker-compose.yml`:

- `DATABASE_URL`: SQLite database path
- `VIDEOS_DIR`: Video storage directory
- `TRANSCRIPTS_DIR`: Transcript storage directory
- `REACT_APP_API_URL`: Backend API URL for frontend

## Troubleshooting

### Common Issues

1. **Port conflicts**: Change ports in `docker-compose.yml` if 3000/8000/80 are in use
2. **Storage permissions**: Ensure Docker has access to the `./data` directory
3. **Memory issues**: Whisper transcription requires sufficient RAM (4GB+ recommended)

### Logs

View application logs:
```bash
docker-compose logs -f backend
docker-compose logs -f frontend
```

## License

MIT License - see LICENSE file for details.
