tagtok.mvt.ar/:1 Unchecked runtime.lastError: The message port closed before a response was received.
tagtok.mvt.ar/:1 Unchecked runtime.lastError: The message port closed before a response was received.
tagtok.mvt.ar/:1 Unchecked runtime.lastError: The message port closed before a response was received.
 Browsing Topics API removed from https://tagtok.mvt.ar/ which is main frame
 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
 Running mobile connection test...
 === MOBILE CONNECTION TEST START ===
 === API URL DETECTION START ===
 Environment REACT_APP_API_URL: undefined
 Current location details: Object
 🌐 Domain access detected!
 Generated API URL (with /api path): https://tagtok.mvt.ar/api
 === API URL DETECTION END ===
 Connection test - API URL determined as: https://tagtok.mvt.ar/api
 Making request to /health endpoint...
 Running mobile connection test...
 === MOBILE CONNECTION TEST START ===
 === API URL DETECTION START ===
 Environment REACT_APP_API_URL: undefined
 Current location details: Object
 🌐 Domain access detected!
 Generated API URL (with /api path): https://tagtok.mvt.ar/api
 === API URL DETECTION END ===
 Connection test - API URL determined as: https://tagtok.mvt.ar/api
 Making request to /health endpoint...
 === API URL DETECTION START ===
 Environment REACT_APP_API_URL: undefined
 Current location details: Object
 🌐 Domain access detected!
api.ts:54 Generated API URL (with /api path): https://tagtok.mvt.ar/api
api.ts:55 === API URL DETECTION END ===
api.ts:88 API Request: GET /health
api.ts:89 Dynamic API Base URL: https://tagtok.mvt.ar/api
api.ts:90 Current hostname: tagtok.mvt.ar
api.ts:91 Full request URL: https://tagtok.mvt.ar/api/health
api.ts:18 === API URL DETECTION START ===
api.ts:22 Environment REACT_APP_API_URL: undefined
api.ts:35 Current location details: Object
api.ts:53 🌐 Domain access detected!
api.ts:54 Generated API URL (with /api path): https://tagtok.mvt.ar/api
api.ts:55 === API URL DETECTION END ===
api.ts:88 API Request: GET /videos?skip=0&limit=24
api.ts:89 Dynamic API Base URL: https://tagtok.mvt.ar/api
api.ts:90 Current hostname: tagtok.mvt.ar
api.ts:91 Full request URL: https://tagtok.mvt.ar/api/videos?skip=0&limit=24
api.ts:18 === API URL DETECTION START ===
api.ts:22 Environment REACT_APP_API_URL: undefined
api.ts:35 Current location details: Object
api.ts:53 🌐 Domain access detected!
api.ts:54 Generated API URL (with /api path): https://tagtok.mvt.ar/api
api.ts:55 === API URL DETECTION END ===
api.ts:88 API Request: GET /tags?skip=0&limit=100&sort_by=usage_count&order=desc
api.ts:89 Dynamic API Base URL: https://tagtok.mvt.ar/api
api.ts:90 Current hostname: tagtok.mvt.ar
api.ts:91 Full request URL: https://tagtok.mvt.ar/api/tags?skip=0&limit=100&sort_by=usage_count&order=desc
api.ts:18 === API URL DETECTION START ===
api.ts:22 Environment REACT_APP_API_URL: undefined
api.ts:35 Current location details: Object
api.ts:53 🌐 Domain access detected!
api.ts:54 Generated API URL (with /api path): https://tagtok.mvt.ar/api
api.ts:55 === API URL DETECTION END ===
api.ts:88 API Request: GET /tags/cloud/data
api.ts:89 Dynamic API Base URL: https://tagtok.mvt.ar/api
api.ts:90 Current hostname: tagtok.mvt.ar
api.ts:91 Full request URL: https://tagtok.mvt.ar/api/tags/cloud/data
api.ts:18 === API URL DETECTION START ===
api.ts:22 Environment REACT_APP_API_URL: undefined
api.ts:35 Current location details: Object
api.ts:53 🌐 Domain access detected!
api.ts:54 Generated API URL (with /api path): https://tagtok.mvt.ar/api
api.ts:55 === API URL DETECTION END ===
api.ts:88 API Request: GET /health
api.ts:89 Dynamic API Base URL: https://tagtok.mvt.ar/api
api.ts:90 Current hostname: tagtok.mvt.ar
api.ts:91 Full request URL: https://tagtok.mvt.ar/api/health
WebSocketClient.js:13 WebSocket connection to 'wss://tagtok.mvt.ar:3000/ws' failed: 
WebSocketClient @ WebSocketClient.js:13
videos:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
/api/health:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
api.ts:121 API Error Details: Object
(anonymous) @ api.ts:121
api.ts:142 API Error: Object
(anonymous) @ api.ts:142
HomePage.tsx:46 Retrying video fetch (attempt 1/3)...
api.ts:121 API Error Details: Object
(anonymous) @ api.ts:121
api.ts:142 API Error: Object
(anonymous) @ api.ts:142
api.ts:165 ❌ Mobile connection test failed!
testMobileConnection @ api.ts:165
api.ts:166 Error details: AxiosError
testMobileConnection @ api.ts:166
api.ts:167 Error message: Request failed with status code 404
testMobileConnection @ api.ts:167
api.ts:168 Error config: Object
testMobileConnection @ api.ts:168
api.ts:169 === MOBILE CONNECTION TEST END ===
api.ts:18 === API URL DETECTION START ===
api.ts:22 Environment REACT_APP_API_URL: undefined
api.ts:35 Current location details: Object
api.ts:53 🌐 Domain access detected!
api.ts:54 Generated API URL (with /api path): https://tagtok.mvt.ar/api
api.ts:55 === API URL DETECTION END ===
HomePage.tsx:27 Connection test result: Object
/api/tags/cloud/data:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
api.ts:121 API Error Details: Object
(anonymous) @ api.ts:121
api.ts:142 API Error: Object
(anonymous) @ api.ts:142
/api/tags?skip=0&limit=100&sort_by=usage_count&order=desc:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
api.ts:121 API Error Details: Object
(anonymous) @ api.ts:121
api.ts:142 API Error: Object
(anonymous) @ api.ts:142
/api/health:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
api.ts:121 API Error Details: Object
(anonymous) @ api.ts:121
api.ts:142 API Error: Object
(anonymous) @ api.ts:142
api.ts:165 ❌ Mobile connection test failed!
testMobileConnection @ api.ts:165
api.ts:166 Error details: AxiosError
testMobileConnection @ api.ts:166
api.ts:167 Error message: Request failed with status code 404
testMobileConnection @ api.ts:167
api.ts:168 Error config: Object
testMobileConnection @ api.ts:168
api.ts:169 === MOBILE CONNECTION TEST END ===
api.ts:18 === API URL DETECTION START ===
api.ts:22 Environment REACT_APP_API_URL: undefined
api.ts:35 Current location details: Object
api.ts:53 🌐 Domain access detected!
api.ts:54 Generated API URL (with /api path): https://tagtok.mvt.ar/api
api.ts:55 === API URL DETECTION END ===
HomePage.tsx:27 Connection test result: Object
api.ts:18 === API URL DETECTION START ===
api.ts:22 Environment REACT_APP_API_URL: undefined
api.ts:35 Current location details: Object
api.ts:53 🌐 Domain access detected!
api.ts:54 Generated API URL (with /api path): https://tagtok.mvt.ar/api
api.ts:55 === API URL DETECTION END ===
api.ts:88 API Request: GET /videos?skip=0&limit=24
api.ts:89 Dynamic API Base URL: https://tagtok.mvt.ar/api
api.ts:90 Current hostname: tagtok.mvt.ar
api.ts:91 Full request URL: https://tagtok.mvt.ar/api/videos?skip=0&limit=24
api.ts:18 === API URL DETECTION START ===
api.ts:22 Environment REACT_APP_API_URL: undefined
api.ts:35 Current location details: Object
api.ts:53 🌐 Domain access detected!
api.ts:54 Generated API URL (with /api path): https://tagtok.mvt.ar/api
api.ts:55 === API URL DETECTION END ===
api.ts:88 API Request: GET /tags/cloud/data
api.ts:89 Dynamic API Base URL: https://tagtok.mvt.ar/api
api.ts:90 Current hostname: tagtok.mvt.ar
api.ts:91 Full request URL: https://tagtok.mvt.ar/api/tags/cloud/data
api.ts:18 === API URL DETECTION START ===
api.ts:22 Environment REACT_APP_API_URL: undefined
api.ts:35 Current location details: Object
api.ts:53 🌐 Domain access detected!
api.ts:54 Generated API URL (with /api path): https://tagtok.mvt.ar/api
api.ts:55 === API URL DETECTION END ===
api.ts:88 API Request: GET /tags?skip=0&limit=100&sort_by=usage_count&order=desc
api.ts:89 Dynamic API Base URL: https://tagtok.mvt.ar/api
api.ts:90 Current hostname: tagtok.mvt.ar
api.ts:91 Full request URL: https://tagtok.mvt.ar/api/tags?skip=0&limit=100&sort_by=usage_count&order=desc
videos:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
api.ts:121 API Error Details: Object
(anonymous) @ api.ts:121
api.ts:142 API Error: Object
(anonymous) @ api.ts:142
HomePage.tsx:46 Retrying video fetch (attempt 2/3)...
/api/tags/cloud/data:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
/api/tags?skip=0&limit=100&sort_by=usage_count&order=desc:1 
            
            
           Failed to load resource: the server responded with a status of 404 ()
api.ts:121 API Error Details: Object
(anonymous) @ api.ts:121
api.ts:142 API Error: Object
(anonymous) @ api.ts:142
api.ts:121 API Error Details: Object
(anonymous) @ api.ts:121
api.ts:142 API Error: Object
(anonymous) @ api.ts:142
WebSocketClient.js:13 WebSocket connection to 'wss://tagtok.mvt.ar:3000/ws' failed: 
WebSocketClient @ WebSocketClient.js:13
api.ts:18 === API URL DETECTION START ===
api.ts:22 Environment REACT_APP_API_URL: undefined
api.ts:35 Current location details: {hostname: 'tagtok.mvt.ar', protocol: 'https:', port: '', href: 'https://tagtok.mvt.ar/', isLocalhost: false, …}
api.ts:53 🌐 Domain access detected!
api.ts:54 Generated API URL (with /api path): https://tagtok.mvt.ar/api
api.ts:55 === API URL DETECTION END ===
api.ts:88 API Request: GET /videos?skip=0&limit=24
api.ts:89 Dynamic API Base URL: https://tagtok.mvt.ar/api
api.ts:90 Current hostname: tagtok.mvt.ar
api.ts:91 Full request URL: https://tagtok.mvt.ar/api/videos?skip=0&limit=24
api.ts:212 
            
            
           GET https://tagtok.mvt.ar/api/videos?skip=0&limit=24 404 (Not Found)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:212
queryFn @ HomePage.tsx:41
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
api.ts:121 API Error Details: {message: 'Request failed with status code 404', status: 404, statusText: '', url: '/videos?skip=0&limit=24', baseURL: 'https://tagtok.mvt.ar/api', …}
(anonymous) @ api.ts:121
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:212
queryFn @ HomePage.tsx:41
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
api.ts:142 API Error: {detail: 'Not Found'}
(anonymous) @ api.ts:142
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:212
queryFn @ HomePage.tsx:41
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
HomePage.tsx:46 Retrying video fetch (attempt 3/3)...
WebSocketClient.js:13 WebSocket connection to 'wss://tagtok.mvt.ar:3000/ws' failed: 
WebSocketClient @ WebSocketClient.js:13
initSocket @ socket.js:27
(anonymous) @ socket.js:51
api.ts:18 === API URL DETECTION START ===
api.ts:22 Environment REACT_APP_API_URL: undefined
api.ts:35 Current location details: {hostname: 'tagtok.mvt.ar', protocol: 'https:', port: '', href: 'https://tagtok.mvt.ar/', isLocalhost: false, …}
api.ts:53 🌐 Domain access detected!
api.ts:54 Generated API URL (with /api path): https://tagtok.mvt.ar/api
api.ts:55 === API URL DETECTION END ===
api.ts:88 API Request: GET /videos?skip=0&limit=24
api.ts:89 Dynamic API Base URL: https://tagtok.mvt.ar/api
api.ts:90 Current hostname: tagtok.mvt.ar
api.ts:91 Full request URL: https://tagtok.mvt.ar/api/videos?skip=0&limit=24
api.ts:212 
            
            
           GET https://tagtok.mvt.ar/api/videos?skip=0&limit=24 404 (Not Found)
dispatchXhrRequest @ xhr.js:195
xhr @ xhr.js:15
dispatchRequest @ dispatchRequest.js:51
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:212
queryFn @ HomePage.tsx:41
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
(anonymous) @ retryer.ts:199
api.ts:121 API Error Details: {message: 'Request failed with status code 404', status: 404, statusText: '', url: '/videos?skip=0&limit=24', baseURL: 'https://tagtok.mvt.ar/api', …}
(anonymous) @ api.ts:121
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:212
queryFn @ HomePage.tsx:41
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
(anonymous) @ retryer.ts:199
api.ts:142 API Error: {detail: 'Not Found'}
(anonymous) @ api.ts:142
Promise.then
_request @ Axios.js:163
request @ Axios.js:40
Axios.<computed> @ Axios.js:213
wrap @ bind.js:5
getVideos @ api.ts:212
queryFn @ HomePage.tsx:41
fetchFn @ query.ts:457
run @ retryer.ts:153
(anonymous) @ retryer.ts:199
Promise.then
(anonymous) @ retryer.ts:194
Promise.catch
run @ retryer.ts:158
(anonymous) @ retryer.ts:199
WebSocketClient.js:13 WebSocket connection to 'wss://tagtok.mvt.ar:3000/ws' failed: 
WebSocketClient @ WebSocketClient.js:13
initSocket @ socket.js:27
(anonymous) @ socket.js:51