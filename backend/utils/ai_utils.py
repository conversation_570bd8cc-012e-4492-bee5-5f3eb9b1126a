import random
import re
from typing import List, Dict, Tuple, Optional
import asyncio
import subprocess
import tempfile
import os

# Simplified imports for testing - will use mock implementations
try:
    import whisper
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False

try:
    from keybert import KeyBERT
    KEYBERT_AVAILABLE = True
except ImportError:
    KEYBERT_AVAILABLE = False

class WhisperTranscriber:
    def __init__(self, model_size: str = "base"):
        """Initialize Whisper transcriber with specified model size"""
        self.model_size = model_size
        self.model = None
        if WHISPER_AVAILABLE:
            self._load_model()

    def _load_model(self):
        """Load Whisper model"""
        if not WHISPER_AVAILABLE:
            print("Whisper not available - using mock transcription")
            return

        try:
            print(f"Loading Whisper model: {self.model_size}")
            self.model = whisper.load_model(self.model_size)
            print("Whisper model loaded successfully")
        except Exception as e:
            print(f"Failed to load Whisper model: {e}")
            self.model = None

    async def transcribe(self, video_path: str) -> Tuple[str, str]:
        """Transcribe video and detect language"""
        if not WHISPER_AVAILABLE or not self.model:
            # Mock transcription for testing
            print(f"Mock transcribing video: {video_path}")
            return "This is a mock transcript for testing purposes. The video contains sample content that would normally be transcribed by Whisper.", "en"

        try:
            # Run transcription in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._transcribe_sync,
                video_path
            )

            transcript = result["text"].strip()
            language = result["language"]

            return transcript, language

        except Exception as e:
            print(f"Transcription error: {e}")
            # Return mock data on error
            return "Error occurred during transcription. This is a fallback transcript.", "en"

    def _transcribe_sync(self, video_path: str) -> dict:
        """Synchronous transcription method"""
        return self.model.transcribe(
            video_path,
            language=None,  # Auto-detect language
            task="transcribe",
            verbose=False
        )

class TagGenerator:
    def __init__(self):
        """Initialize tag generator with NLP models"""
        self.keybert_model = None
        self.nlp = None
        if KEYBERT_AVAILABLE or SPACY_AVAILABLE:
            self._load_models()

        # Predefined color palette for tags
        self.colors = [
            "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
            "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9",
            "#F8C471", "#82E0AA", "#F1948A", "#85C1E9", "#D7BDE2",
            "#A3E4D7", "#F9E79F", "#D5A6BD", "#AED6F1", "#A9DFBF"
        ]

    def _load_models(self):
        """Load NLP models"""
        try:
            # Load KeyBERT for keyword extraction
            if KEYBERT_AVAILABLE:
                print("Loading KeyBERT model...")
                self.keybert_model = KeyBERT()
                print("KeyBERT model loaded successfully")
            else:
                print("KeyBERT not available - using fallback tagging")

            # Try to load spaCy model
            if SPACY_AVAILABLE:
                try:
                    print("Loading spaCy model...")
                    self.nlp = spacy.load("en_core_web_sm")
                    print("spaCy model loaded successfully")
                except OSError:
                    print("spaCy en_core_web_sm model not found. Using basic processing.")
                    self.nlp = None
            else:
                print("spaCy not available - using basic processing")

        except Exception as e:
            print(f"Failed to load NLP models: {e}")
    
    async def generate_tags(self, text: str, max_tags: int = 5) -> List[Dict[str, str]]:
        """Generate tags from text content"""
        if not text.strip():
            return []

        # If no AI libraries available, use fallback immediately
        if not KEYBERT_AVAILABLE and not SPACY_AVAILABLE:
            return self._fallback_tags(text, max_tags)

        try:
            # Run tag generation in executor to avoid blocking
            loop = asyncio.get_event_loop()
            tags = await loop.run_in_executor(
                None,
                self._generate_tags_sync,
                text,
                max_tags
            )
            return tags
        except Exception as e:
            print(f"Tag generation error: {e}")
            return self._fallback_tags(text, max_tags)
    
    def _generate_tags_sync(self, text: str, max_tags: int = 5) -> List[Dict[str, str]]:
        """Synchronous tag generation"""
        tags = []

        try:
            # Method 1: Use KeyBERT for keyword extraction
            if KEYBERT_AVAILABLE and self.keybert_model:
                keywords = self.keybert_model.extract_keywords(
                    text,
                    keyphrase_ngram_range=(1, 2),
                    stop_words='english',
                    top_k=max_tags * 2,
                    diversity=0.7
                )

                for keyword, _ in keywords:  # Ignore score for now
                    if len(tags) >= max_tags:
                        break

                    # Clean and format keyword
                    tag_name = self._clean_tag_name(keyword)
                    if tag_name and len(tag_name) > 2:
                        tags.append({
                            "name": tag_name,
                            "color": random.choice(self.colors),
                            "description": self._generate_tag_description(tag_name, text)
                        })

            # Method 2: Use spaCy for entity extraction (if available)
            if SPACY_AVAILABLE and self.nlp and len(tags) < max_tags:
                doc = self.nlp(text)

                # Extract named entities
                for ent in doc.ents:
                    if len(tags) >= max_tags:
                        break

                    if ent.label_ in ["PERSON", "ORG", "GPE", "EVENT", "PRODUCT"]:
                        tag_name = self._clean_tag_name(ent.text)
                        if tag_name and len(tag_name) > 2:
                            # Check if tag already exists
                            if not any(tag["name"].lower() == tag_name.lower() for tag in tags):
                                tags.append({
                                    "name": tag_name,
                                    "color": random.choice(self.colors),
                                    "description": f"{ent.label_}: {tag_name}"
                                })

            # Method 3: Simple frequency-based extraction as fallback
            if len(tags) < max_tags:
                simple_tags = self._extract_simple_tags(text, max_tags - len(tags))
                tags.extend(simple_tags)

        except Exception as e:
            print(f"Error in tag generation: {e}")
            return self._fallback_tags(text, max_tags)

        return tags[:max_tags]
    
    def _clean_tag_name(self, text: str) -> str:
        """Clean and format tag name"""
        # Remove special characters and extra spaces
        cleaned = re.sub(r'[^\w\s-]', '', text)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        # Capitalize first letter of each word
        cleaned = ' '.join(word.capitalize() for word in cleaned.split())
        
        return cleaned
    
    def _generate_tag_description(self, tag_name: str, context: str) -> str:
        """Generate a simple description for a tag based on context"""
        # Simple heuristic-based description generation
        context_lower = context.lower()
        tag_lower = tag_name.lower()
        
        if any(word in context_lower for word in ['music', 'song', 'dance', 'beat']):
            return f"Music/Audio related to {tag_name}"
        elif any(word in context_lower for word in ['food', 'cooking', 'recipe', 'eat']):
            return f"Food/Cooking content about {tag_name}"
        elif any(word in context_lower for word in ['travel', 'place', 'location', 'visit']):
            return f"Travel/Location content featuring {tag_name}"
        elif any(word in context_lower for word in ['funny', 'comedy', 'laugh', 'joke']):
            return f"Comedy/Entertainment content about {tag_name}"
        elif any(word in context_lower for word in ['tutorial', 'how to', 'learn', 'teach']):
            return f"Educational content about {tag_name}"
        else:
            return f"Content related to {tag_name}"
    
    def _extract_simple_tags(self, text: str, max_tags: int) -> List[Dict[str, str]]:
        """Simple frequency-based tag extraction"""
        # Split text into words and count frequency
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        
        # Common stop words to exclude
        stop_words = {
            'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had',
            'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his',
            'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy',
            'did', 'man', 'men', 'put', 'say', 'she', 'too', 'use', 'way', 'will',
            'this', 'that', 'with', 'have', 'from', 'they', 'know', 'want', 'been',
            'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just',
            'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them',
            'well', 'were', 'what', 'your'
        }
        
        # Filter and count words
        word_freq = {}
        for word in words:
            if word not in stop_words and len(word) > 3:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Get most frequent words
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        
        tags = []
        for word, freq in sorted_words[:max_tags]:
            if freq > 1:  # Only include words that appear more than once
                tags.append({
                    "name": word.capitalize(),
                    "color": random.choice(self.colors),
                    "description": f"Frequently mentioned topic"
                })
        
        return tags
    
    def _fallback_tags(self, text: str, max_tags: int) -> List[Dict[str, str]]:
        """Fallback tag generation when AI models fail"""
        # Very simple fallback - extract capitalized words
        words = re.findall(r'\b[A-Z][a-z]+\b', text)
        unique_words = list(set(words))[:max_tags]
        
        return [
            {
                "name": word,
                "color": random.choice(self.colors),
                "description": f"Topic: {word}"
            }
            for word in unique_words
        ]
