from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

# Video schemas
class VideoBase(BaseModel):
    title: Optional[str] = None
    original_filename: str

class VideoCreate(VideoBase):
    pass

class VideoUpdate(BaseModel):
    title: Optional[str] = None

class VideoResponse(VideoBase):
    id: int
    filename: str
    file_path: str
    file_size: int
    duration: Optional[float] = None
    width: Optional[int] = None
    height: Optional[int] = None
    fps: Optional[float] = None
    thumbnail_path: Optional[str] = None
    transcript: Optional[str] = None
    transcript_language: Optional[str] = None
    upload_date: datetime
    processed: bool
    processing_status: str
    tags: List["TagResponse"] = []
    
    class Config:
        from_attributes = True

# Tag schemas
class TagBase(BaseModel):
    name: str
    color: str = Field(..., pattern=r'^#[0-9A-Fa-f]{6}$')  # Hex color validation
    description: Optional[str] = None

class TagCreate(TagBase):
    pass

class TagUpdate(BaseModel):
    name: Optional[str] = None
    color: Optional[str] = Field(None, pattern=r'^#[0-9A-Fa-f]{6}$')
    description: Optional[str] = None

class TagResponse(TagBase):
    id: int
    created_date: datetime
    usage_count: int
    
    class Config:
        from_attributes = True

# Processing job schemas
class ProcessingJobResponse(BaseModel):
    id: int
    video_id: int
    job_type: str
    status: str
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    
    class Config:
        from_attributes = True

# Search and filter schemas
class VideoFilter(BaseModel):
    tags: Optional[List[str]] = None
    search: Optional[str] = None
    language: Optional[str] = None
    processed: Optional[bool] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None

class PaginationParams(BaseModel):
    skip: int = Field(0, ge=0)
    limit: int = Field(50, ge=1, le=100)

# Analytics schemas
class AnalyticsResponse(BaseModel):
    total_videos: int
    total_tags: int
    total_duration: float
    processed_videos: int
    pending_videos: int
    top_tags: List[Dict[str, Any]]
    language_distribution: Dict[str, int]
    upload_timeline: List[Dict[str, Any]]
    duration_distribution: Dict[str, int]

# Export schemas
class ExportFormat(BaseModel):
    format: str = Field(..., pattern=r'^(csv|json)$')
    include_transcript: bool = True
    include_tags: bool = True

# Upload response
class UploadResponse(BaseModel):
    message: str
    uploaded_files: List[str]
    failed_files: List[Dict[str, str]]
    total_uploaded: int

# Error response
class ErrorResponse(BaseModel):
    detail: str
    error_code: Optional[str] = None

# Health check response
class HealthResponse(BaseModel):
    status: str
    timestamp: datetime
    version: str = "1.0.0"

# Update forward references
VideoResponse.model_rebuild()
